# Cloudflare Turnstile Implementation Guide

This guide explains how to implement Cloudflare Turnstile (a CAPTCHA alternative) in your React frontend and Spring Boot backend.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Client-Side Implementation](#client-side-implementation)
3. [Server-Side Validation](#server-side-validation)
4. [Configuration Options](#configuration-options)
5. [Best Practices](#best-practices)
6. [Troubleshooting](#troubleshooting)

## Prerequisites

Before you begin, you need to:

1. Create a Cloudflare account if you don't have one already
2. Register a new Turnstile site at [Cloudflare Turnstile Dashboard](https://dash.cloudflare.com/?to=/:account/turnstile)
3. Get your site key and secret key

## Client-Side Implementation

### 1. Install the Turnstile React component

```bash
npm install react-turnstile
```

### 2. Add environment variables

Create or update your `.env` file with your Turnstile site key:

```
VITE_TURNSTILE_SITE_KEY=your_site_key_here
```

### 3. Import and use the Turnstile component

```jsx
import React, { useState, useRef } from "react";
import Turnstile from "react-turnstile";

function YourForm() {
  const [turnstileToken, setTurnstileToken] = useState(null);
  const turnstileRef = useRef(null);
  
  const handleSubmit = async (values) => {
    // Check if Turnstile token is available
    if (!turnstileToken) {
      // Show error message
      return;
    }
    
    // Include token in your form submission
    const dataWithToken = {
      ...values,
      turnstileToken: turnstileToken
    };
    
    // Submit form data with token
    // ...
  };
  
  return (
    <form onSubmit={handleSubmit}>
      {/* Your form fields */}
      
      <div className="turnstile-container">
        <Turnstile
          ref={turnstileRef}
          sitekey="your_site_key_here"
          onVerify={(token) => setTurnstileToken(token)}
          onError={() => setTurnstileToken(null)}
          onExpire={() => setTurnstileToken(null)}
          theme="light"
        />
      </div>
      
      <button type="submit">Submit</button>
    </form>
  );
}
```

### 4. Add CSS for the Turnstile container

```css
.turnstile-container {
  display: flex;
  justify-content: center;
  margin: 10px 0;
}

@media (max-width: 480px) {
  .turnstile-container {
    transform: scale(0.9);
    transform-origin: center;
  }
}
```

## Server-Side Validation

### 1. Create a validation utility class

```java
package com.yourapp.util;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

@Component
public class TurnstileValidator {

    private static final String TURNSTILE_VERIFY_URL = "https://challenges.cloudflare.com/turnstile/v0/siteverify";
    
    @Value("${turnstile.secret.key}")
    private String secretKey;
    
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    
    public TurnstileValidator() {
        this.restTemplate = new RestTemplate();
        this.objectMapper = new ObjectMapper();
    }
    
    public boolean validateToken(String token, String remoteIp) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            
            MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
            formData.add("secret", secretKey);
            formData.add("response", token);
            
            if (remoteIp != null && !remoteIp.isEmpty()) {
                formData.add("remoteip", remoteIp);
            }
            
            HttpEntity<MultiValueMap<String, String>> requestEntity = 
                new HttpEntity<>(formData, headers);
            
            ResponseEntity<String> response = restTemplate.postForEntity(
                TURNSTILE_VERIFY_URL, 
                requestEntity, 
                String.class
            );
            
            JsonNode jsonResponse = objectMapper.readTree(response.getBody());
            return jsonResponse.get("success").asBoolean();
            
        } catch (Exception e) {
            System.err.println("Error validating Turnstile token: " + e.getMessage());
            return false;
        }
    }
}
```

### 2. Add the secret key to application.properties

```properties
# Cloudflare Turnstile Configuration
turnstile.secret.key=your_secret_key_here
```

### 3. Use the validator in your controller

```java
@RestController
@CrossOrigin(origins = {"http://localhost:5173"}, allowCredentials = "true")
public class SignupController {
    
    @Autowired
    private TurnstileValidator turnstileValidator;
    
    @PostMapping("/signup")
    public ResponseEntity<?> signup(@RequestBody Map<String, Object> signupRequest, HttpServletRequest request) {
        try {
            // Extract the Turnstile token
            String turnstileToken = (String) signupRequest.get("turnstileToken");
            
            if (turnstileToken == null || turnstileToken.isEmpty()) {
                return ResponseEntity.badRequest().body("Security verification token is required");
            }
            
            // Get client IP
            String clientIp = request.getRemoteAddr();
            
            // Validate the token
            boolean isValid = turnstileValidator.validateToken(turnstileToken, clientIp);
            
            if (!isValid) {
                return ResponseEntity.badRequest().body("Security verification failed");
            }
            
            // Remove the token before processing
            signupRequest.remove("turnstileToken");
            
            // Process registration
            // ...
            
            return ResponseEntity.ok("User registered successfully");
            
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("Error during registration: " + e.getMessage());
        }
    }
}
```

## Configuration Options

### Client-Side Options

The Turnstile component accepts several props:

- `sitekey` (required): Your Turnstile site key
- `action`: Custom action name for analytics
- `cData`: Custom data to include in the token
- `theme`: 'light' or 'dark'
- `size`: 'normal' or 'compact'
- `language`: Language code (e.g., 'en', 'ar')
- `refreshExpired`: 'auto' or 'manual'
- `appearance`: 'always' or 'execute' or 'interaction-only'

### Server-Side Options

When validating tokens, you can include:

- `remoteip`: Client IP address for additional security
- `idempotency_key`: Unique key to prevent duplicate validations

## Best Practices

1. **Store keys securely**: Never expose your secret key in client-side code
2. **Handle errors gracefully**: Provide clear feedback when validation fails
3. **Implement proper timeout handling**: Reset the widget if it expires
4. **Use appropriate theme**: Match your site's design with the right theme
5. **Consider accessibility**: Ensure your form remains accessible
6. **Test thoroughly**: Test with real users before deploying to production

## Troubleshooting

### Common Issues

1. **Token validation fails**: Check that your secret key is correct
2. **Widget doesn't appear**: Verify your site key and check for JavaScript errors
3. **Widget appears but doesn't work**: Check network connectivity and CORS settings
4. **Token expires too quickly**: Implement proper handling for expired tokens

### Debugging

- Check browser console for errors
- Verify network requests to Cloudflare's verification endpoint
- Test with Cloudflare's demo site to rule out account issues

## Resources

- [Cloudflare Turnstile Documentation](https://developers.cloudflare.com/turnstile/)
- [Turnstile React Component](https://www.npmjs.com/package/react-turnstile)
- [Server-Side Validation Guide](https://developers.cloudflare.com/turnstile/get-started/server-side-validation/)
