#!/bin/bash

echo "🚀 Starting build process for b2bproject-service..."

# Run Maven clean package
echo "🔨 Running Maven clean build..."
./mvnw clean package -DskipTests=true || { echo "❌ Maven build failed!"; exit 1; }

# Find the built JAR file
JAR_FILE=$(find target -name "b2bproject-*.jar" | head -n 1)

# Check if JAR file exists
if [ -z "$JAR_FILE" ]; then
    echo "❌ Error: JAR file not found! Run 'mvn clean package' first."
    exit 1
fi

echo "✅ Found JAR file: $JAR_FILE"

# Rename it to a standard name for Docker
mv "$JAR_FILE" target/userprofile.jar

# Build the Docker image
echo "🐳 Building Docker image for b2b-service..."
docker build -t user-profile-service . || { echo "❌ Docker build failed!"; exit 1; }

# Remove old container (if exists)
#docker rm -f b2bproject-service 2>/dev/null

# Run the container
#docker run -d --name b2bproject-service --network b2b-network -p 8082:8082 b2bproject-service

#echo "✅ user-management-service is up and running!"
