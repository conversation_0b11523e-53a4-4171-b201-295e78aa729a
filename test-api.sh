#!/bin/bash

# Test script for vendor profile API endpoints
# Make sure the backend is running on localhost:8080

BASE_URL="http://localhost:8080"
USER_ID="056fe13d-e15b-4166-91d3-67833810fc0e"

echo "=== Testing Vendor Profile API Endpoints ==="
echo "Base URL: $BASE_URL"
echo "User ID: $USER_ID"
echo ""

# Test 1: Health check
echo "1. Testing health check endpoint..."
curl -X GET "$BASE_URL/api/profiles/health" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n" \
  -s | jq '.' 2>/dev/null || echo "Response received (jq not available for formatting)"
echo ""

# Test 2: Check profile existence
echo "2. Testing profile existence check..."
curl -X GET "$BASE_URL/api/profiles/exists/$USER_ID" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n" \
  -s | jq '.' 2>/dev/null || echo "Response received (jq not available for formatting)"
echo ""

# Test 3: Debug vendor profile
echo "3. Testing debug vendor profile endpoint..."
curl -X GET "$BASE_URL/api/profiles/debug/vendor/$USER_ID" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n" \
  -s | jq '.' 2>/dev/null || echo "Response received (jq not available for formatting)"
echo ""

# Test 4: Get vendor profile (main endpoint that's failing)
echo "4. Testing vendor profile retrieval (main endpoint)..."
curl -X GET "$BASE_URL/api/profiles/vendor/$USER_ID" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n" \
  -s | jq '.' 2>/dev/null || echo "Response received (jq not available for formatting)"
echo ""

# Test 5: Alternative vendor endpoint
echo "5. Testing alternative vendor endpoint..."
curl -X GET "$BASE_URL/api/vendor/user/$USER_ID" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n" \
  -s | jq '.' 2>/dev/null || echo "Response received (jq not available for formatting)"
echo ""

# Test 6: Create vendor profile if it doesn't exist
echo "6. Testing vendor profile creation..."
curl -X POST "$BASE_URL/api/profiles/vendor" \
  -H "Content-Type: application/json" \
  -d "{
    \"userId\": \"$USER_ID\",
    \"businessName\": \"Test Business API\",
    \"businessEmail\": \"<EMAIL>\",
    \"businessPhone\": \"******-TEST\",
    \"businessCategory\": \"Technology\",
    \"profileComplete\": false
  }" \
  -w "\nStatus: %{http_code}\n" \
  -s | jq '.' 2>/dev/null || echo "Response received (jq not available for formatting)"
echo ""

# Test 7: Try to get vendor profile again after creation
echo "7. Testing vendor profile retrieval after creation..."
curl -X GET "$BASE_URL/api/profiles/vendor/$USER_ID" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n" \
  -s | jq '.' 2>/dev/null || echo "Response received (jq not available for formatting)"
echo ""

echo "=== Test completed ==="
echo "If you see 404 errors, check if the backend is running on port 8080"
echo "If you see connection errors, make sure PostgreSQL is running on port 7010"
