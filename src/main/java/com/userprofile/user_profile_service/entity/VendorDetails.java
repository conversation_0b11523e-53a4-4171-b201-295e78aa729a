package com.userprofile.user_profile_service.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "vendor_details")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VendorDetails {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id", unique = true)
    private String userId;

    // Business Basic Details
    @Column(name = "business_name")
    private String businessName;

    @Column(name = "business_logo")
    private String businessLogo;

    @Column(name = "business_email")
    private String businessEmail;

    @Column(name = "business_phone")
    private String businessPhone;

    // Business Address
    @Column(name = "street_address")
    private String streetAddress;

    @Column(name = "city")
    private String city;

    @Column(name = "state")
    private String state;

    @Column(name = "zip_code")
    private String zipCode;

    @Column(name = "country")
    private String country;

    // Business Details
    @Column(name = "business_category")
    private String businessCategory;

    @Column(name = "business_subcategory")
    private String businessSubcategory;

    @Column(name = "business_website")
    private String businessWebsite;

    @Column(name = "years_of_experience")
    private String yearsOfExperience;

    @Column(name = "certificate")
    private String certificate;

    // Product Details
    @Column(name = "product_name")
    private String productName;

    @Column(name = "product_description")
    private String productDescription;

    @Column(name = "product_category")
    private String productCategory;

    // Preferences
    @Column(name = "registration_number")
    private String registrationNumber;

    @Column(name = "regions_supported")
    private String regionsSupported;

    @Column(name = "license_details")
    private String licenseDetails;

    @Column(name = "profile_complete")
    private boolean profileComplete;

}