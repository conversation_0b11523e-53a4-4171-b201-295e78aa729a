package com.userprofile.user_profile_service.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.userprofile.user_profile_service.entity.CustomerDetails;
import com.userprofile.user_profile_service.repository.CustomerDetailsRepository;
import com.userprofile.user_profile_service.service.ProfileService;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * REST controller for customer-specific operations.
 */
@RestController
@RequestMapping("/api/customer")
@CrossOrigin(origins = {"http://localhost:5173", "http://localhost:3000", "http://localhost:8081"})
public class CustomerController {

    @Autowired
    private CustomerDetailsRepository customerRepository;
    
    @Autowired
    private ProfileService profileService;

    /**
     * Get all customers
     * 
     * @return List of all customers
     */
    @GetMapping
    public ResponseEntity<List<CustomerDetails>> getAllCustomers() {
        List<CustomerDetails> customers = customerRepository.findAll();
        return ResponseEntity.ok(customers);
    }

    /**
     * Get customer by database ID
     * 
     * @param id The database ID
     * @return The customer if found
     */
    @GetMapping("/{id}")
    public ResponseEntity<CustomerDetails> getCustomerById(@PathVariable Long id) {
        CustomerDetails customer = customerRepository.findById(id).orElse(null);
        if (customer != null) {
            return ResponseEntity.ok(customer);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Get customer by user ID (UUID)
     * 
     * @param userId The user ID (UUID)
     * @return The customer if found
     */
    @GetMapping("/user/{userId}")
    public ResponseEntity<?> getCustomerByUserId(@PathVariable String userId) {
        Optional<CustomerDetails> optionalCustomer = customerRepository.findByUserId(userId);
        
        if (optionalCustomer.isPresent()) {
            return ResponseEntity.ok(optionalCustomer.get());
        } else {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("message", "Customer not found for user ID: " + userId));
        }
    }

    /**
     * Create a new customer
     * 
     * @param customer The customer details to save
     * @return The created customer
     */
    @PostMapping
    public ResponseEntity<CustomerDetails> createCustomer(@RequestBody CustomerDetails customer) {
        CustomerDetails savedCustomer = customerRepository.save(customer);
        return ResponseEntity.status(HttpStatus.CREATED).body(savedCustomer);
    }

    /**
     * Update customer by database ID
     * 
     * @param id The database ID
     * @param customer The updated customer details
     * @return Response with update status
     */
    @PutMapping("/{id}")
    public ResponseEntity<?> updateCustomer(@PathVariable Long id, @RequestBody CustomerDetails customer) {
        if (!customerRepository.existsById(id)) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("message", "Customer not found with ID: " + id));
        }
        
        CustomerDetails existingCustomer = customerRepository.findById(id).get();
        
        // Update all fields
        existingCustomer.setUserId(customer.getUserId());

        // Personal Information
        existingCustomer.setFirstName(customer.getFirstName());
        existingCustomer.setLastName(customer.getLastName());
        existingCustomer.setEmailAddress(customer.getEmailAddress());
        existingCustomer.setPhoneNumber(customer.getPhoneNumber());
        existingCustomer.setDateOfBirth(customer.getDateOfBirth());
        existingCustomer.setProfilePicture(customer.getProfilePicture());

        // Primary Address
        existingCustomer.setPrimaryStreetAddress(customer.getPrimaryStreetAddress());
        existingCustomer.setPrimaryCity(customer.getPrimaryCity());
        existingCustomer.setPrimaryState(customer.getPrimaryState());
        existingCustomer.setPrimaryZipCode(customer.getPrimaryZipCode());
        existingCustomer.setCountry(customer.getCountry());

        // Secondary Address
        existingCustomer.setSecondaryStreetAddress(customer.getSecondaryStreetAddress());
        existingCustomer.setSecondaryCity(customer.getSecondaryCity());
        existingCustomer.setSecondaryState(customer.getSecondaryState());
        existingCustomer.setSecondaryZipCode(customer.getSecondaryZipCode());

        // Business Details
        existingCustomer.setDesignationOrRole(customer.getDesignationOrRole());
        existingCustomer.setIndustryOrDomain(customer.getIndustryOrDomain());
        existingCustomer.setCompanyName(customer.getCompanyName());
        existingCustomer.setPreferredVendorType(customer.getPreferredVendorType());
        existingCustomer.setSubcategoriesOfInterest(customer.getSubcategoriesOfInterest());
        existingCustomer.setCategoriesOfServicesNeeded(customer.getCategoriesOfServicesNeeded());

        // Preferences
        existingCustomer.setReceiveNotificationsFromVendors(customer.isReceiveNotificationsFromVendors());
        existingCustomer.setVisibleToVendors(customer.isVisibleToVendors());
        existingCustomer.setProfileComplete(customer.isProfileComplete());

        CustomerDetails updatedCustomer = customerRepository.save(existingCustomer);
        return ResponseEntity.ok(updatedCustomer);
    }

    /**
     * Update customer by user ID (UUID)
     * 
     * @param userId The user ID (UUID)
     * @param customer The updated customer details
     * @return Response with update status
     */
    @PutMapping("/user/{userId}")
    public ResponseEntity<?> updateCustomerByUserId(@PathVariable String userId, @RequestBody CustomerDetails customer) {
        Optional<CustomerDetails> optionalCustomer = customerRepository.findByUserId(userId);
        
        if (optionalCustomer.isPresent()) {
            CustomerDetails existingCustomer = optionalCustomer.get();
            customer.setId(existingCustomer.getId());
            CustomerDetails updatedCustomer = profileService.saveCustomerProfile(customer);
            return ResponseEntity.ok(updatedCustomer);
        } else {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("message", "Customer not found for user ID: " + userId));
        }
    }

    /**
     * Delete customer by database ID
     * 
     * @param id The database ID
     * @return Response with deletion status
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteCustomer(@PathVariable Long id) {
        if (!customerRepository.existsById(id)) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("message", "Customer not found with ID: " + id));
        }
        
        customerRepository.deleteById(id);
        return ResponseEntity.ok(Map.of("message", "Customer deleted successfully"));
    }
}
