package com.userprofile.user_profile_service.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/db-health")
public class DatabaseHealthController {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @GetMapping
    public Map<String, Object> checkDatabaseHealth() {
        Map<String, Object> response = new HashMap<>();

        try {
            // Check if we can execute a query
            List<String> tables = jdbcTemplate.queryForList(
                    "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'",
                    String.class);

            response.put("status", "connected");
            response.put("tables", tables);

            // Check if our specific tables exist
            boolean customerTableExists = tables.contains("customer_details");
            boolean vendorTableExists = tables.contains("vendor_details");
            boolean vendorDocumentsTableExists = tables.contains("vendor_documents");

            response.put("customerTableExists", customerTableExists);
            response.put("vendorTableExists", vendorTableExists);
            response.put("vendorDocumentsTableExists", vendorDocumentsTableExists);

            // If tables exist, count rows
            if (customerTableExists) {
                Integer customerCount = jdbcTemplate.queryForObject(
                        "SELECT COUNT(*) FROM customer_details", Integer.class);
                response.put("customerCount", customerCount);
            }

            if (vendorTableExists) {
                Integer vendorCount = jdbcTemplate.queryForObject(
                        "SELECT COUNT(*) FROM vendor_details", Integer.class);
                response.put("vendorCount", vendorCount);
            }

            if (vendorDocumentsTableExists) {
                Integer documentsCount = jdbcTemplate.queryForObject(
                        "SELECT COUNT(*) FROM vendor_documents", Integer.class);
                response.put("documentsCount", documentsCount);
            }

        } catch (Exception e) {
            response.put("status", "error");
            response.put("message", e.getMessage());
        }

        return response;
    }
}