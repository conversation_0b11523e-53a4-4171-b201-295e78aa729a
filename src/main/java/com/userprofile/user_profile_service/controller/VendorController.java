package com.userprofile.user_profile_service.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.userprofile.user_profile_service.entity.VendorDetails;
import com.userprofile.user_profile_service.repository.VendorDetailsRepository;
import com.userprofile.user_profile_service.service.ProfileService;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * REST controller for vendor-specific operations.
 */
@RestController
@RequestMapping("/api/vendor")
@CrossOrigin(origins = {"http://localhost:5173", "http://localhost:3000", "http://localhost:8081"})
public class VendorController {

    @Autowired
    private VendorDetailsRepository vendorRepository;
    
    @Autowired
    private ProfileService profileService;

    /**
     * Get all vendors
     * 
     * @return List of all vendors
     */
    @GetMapping
    public ResponseEntity<List<VendorDetails>> getAllVendors() {
        List<VendorDetails> vendors = vendorRepository.findAll();
        return ResponseEntity.ok(vendors);
    }

    /**
     * Get vendor by database ID
     * 
     * @param id The database ID
     * @return The vendor if found
     */
    @GetMapping("/{id}")
    public ResponseEntity<VendorDetails> getVendorById(@PathVariable Long id) {
        VendorDetails vendor = vendorRepository.findById(id).orElse(null);
        if (vendor != null) {
            return ResponseEntity.ok(vendor);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Get vendor by user ID (UUID)
     * 
     * @param userId The user ID (UUID)
     * @return The vendor if found
     */
    @GetMapping("/user/{userId}")
    public ResponseEntity<?> getVendorByUserId(@PathVariable String userId) {
        Optional<VendorDetails> optionalVendor = vendorRepository.findByUserId(userId);
        
        if (optionalVendor.isPresent()) {
            return ResponseEntity.ok(optionalVendor.get());
        } else {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("message", "Vendor not found for user ID: " + userId));
        }
    }

    /**
     * Create a new vendor
     * 
     * @param vendor The vendor details to save
     * @return The created vendor
     */
    @PostMapping
    public ResponseEntity<VendorDetails> createVendor(@RequestBody VendorDetails vendor) {
        VendorDetails savedVendor = vendorRepository.save(vendor);
        return ResponseEntity.status(HttpStatus.CREATED).body(savedVendor);
    }

    /**
     * Update vendor by database ID
     * 
     * @param id The database ID
     * @param vendor The updated vendor details
     * @return Response with update status
     */
    @PutMapping("/user/{id}")
    public ResponseEntity<?> updateVendor(@PathVariable Long id, @RequestBody VendorDetails vendor) {
        if (!vendorRepository.existsById(id)) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("message", "Vendor not found with ID: " + id));
        }
        
        VendorDetails existingVendor = vendorRepository.findById(id).get();
        
        // Update all fields
        existingVendor.setUserId(vendor.getUserId());

        // Business Basic Details
        existingVendor.setBusinessName(vendor.getBusinessName());
        existingVendor.setBusinessLogo(vendor.getBusinessLogo());
        existingVendor.setBusinessEmail(vendor.getBusinessEmail());
        existingVendor.setBusinessPhone(vendor.getBusinessPhone());

        // Business Address
        existingVendor.setStreetAddress(vendor.getStreetAddress());
        existingVendor.setCity(vendor.getCity());
        existingVendor.setState(vendor.getState());
        existingVendor.setZipCode(vendor.getZipCode());
        existingVendor.setCountry(vendor.getCountry());

        // Business Details
        existingVendor.setBusinessCategory(vendor.getBusinessCategory());
        existingVendor.setBusinessSubcategory(vendor.getBusinessSubcategory());
        existingVendor.setBusinessWebsite(vendor.getBusinessWebsite());
        existingVendor.setYearsOfExperience(vendor.getYearsOfExperience());
        existingVendor.setCertificate(vendor.getCertificate());

        // Product Details
        existingVendor.setProductName(vendor.getProductName());
        existingVendor.setProductDescription(vendor.getProductDescription());
        existingVendor.setProductCategory(vendor.getProductCategory());

        // Preferences
        existingVendor.setRegistrationNumber(vendor.getRegistrationNumber());
        existingVendor.setRegionsSupported(vendor.getRegionsSupported());
        existingVendor.setLicenseDetails(vendor.getLicenseDetails());
        existingVendor.setProfileComplete(vendor.isProfileComplete());

        VendorDetails updatedVendor = vendorRepository.save(existingVendor);
        return ResponseEntity.ok(updatedVendor);
    }

    /**
     * Update vendor by user ID (UUID)
     * 
     * @param userId The user ID (UUID)
     * @param vendor The updated vendor details
     * @return Response with update status
     */
    @PutMapping("/user/{userId}")
    public ResponseEntity<?> updateVendorByUserId(@PathVariable String userId, @RequestBody VendorDetails vendor) {
        Optional<VendorDetails> optionalVendor = vendorRepository.findByUserId(userId);
        
        if (optionalVendor.isPresent()) {
            VendorDetails existingVendor = optionalVendor.get();
            vendor.setId(existingVendor.getId());
            VendorDetails updatedVendor = profileService.saveVendorProfile(vendor);
            return ResponseEntity.ok(updatedVendor);
        } else {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("message", "Vendor not found for user ID: " + userId));
        }
    }

    /**
     * Delete vendor by database ID
     * 
     * @param id The database ID
     * @return Response with deletion status
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteVendor(@PathVariable Long id) {
        if (!vendorRepository.existsById(id)) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("message", "Vendor not found with ID: " + id));
        }
        
        vendorRepository.deleteById(id);
        return ResponseEntity.ok(Map.of("message", "Vendor deleted successfully"));
    }
}
