package com.userprofile.user_profile_service.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import com.userprofile.user_profile_service.entity.VendorDetails;
import com.userprofile.user_profile_service.entity.VendorDocument;
import com.userprofile.user_profile_service.repository.VendorDetailsRepository;
import com.userprofile.user_profile_service.service.VendorDocumentService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * REST controller for vendor document operations
 */
@RestController
@RequestMapping("/api/vendor-documents")
@CrossOrigin(origins = {"http://localhost:5173", "http://localhost:3000", "http://localhost:8081"})
public class VendorDocumentController {

    @Autowired
    private VendorDocumentService vendorDocumentService;

    @Autowired
    private VendorDetailsRepository vendorDetailsRepository;

    /**
     * Get all documents for a vendor by user ID
     *
     * @param userId The user ID
     * @return List of vendor documents
     */
    @GetMapping("/user/{userId}")
    public ResponseEntity<?> getDocumentsByUserId(@PathVariable String userId) {
        Optional<VendorDetails> vendorOpt = vendorDetailsRepository.findByUserId(userId);
        if (vendorOpt.isEmpty()) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("message", "Vendor not found for user ID: " + userId));
        }

        List<VendorDocument> documents = vendorDocumentService.getDocumentsByVendorId(vendorOpt.get().getId());
        
        // Transform the documents to include download URLs
        List<Map<String, Object>> transformedDocs = documents.stream()
                .map(doc -> {
                    Map<String, Object> docMap = new HashMap<>();
                    docMap.put("id", doc.getId());
                    docMap.put("fileName", doc.getFileName());
                    docMap.put("originalFileName", doc.getOriginalFileName());
                    docMap.put("fileType", doc.getFileType());
                    docMap.put("fileSize", doc.getFileSize());
                    docMap.put("description", doc.getDescription());
                    docMap.put("uploadDate", doc.getUploadDate());
                    
                    // Create download URL
                    String fileDownloadUri = ServletUriComponentsBuilder.fromCurrentContextPath()
                            .path("/api/files/download/")
                            .path(doc.getFileName())
                            .toUriString();
                    docMap.put("fileDownloadUri", fileDownloadUri);
                    
                    // Create view URL
                    String fileViewUri = ServletUriComponentsBuilder.fromCurrentContextPath()
                            .path("/api/files/view/")
                            .path(doc.getFileName())
                            .toUriString();
                    docMap.put("fileViewUri", fileViewUri);
                    
                    return docMap;
                })
                .collect(Collectors.toList());
        
        return ResponseEntity.ok(transformedDocs);
    }

    /**
     * Upload a document for a vendor
     *
     * @param userId The user ID
     * @param file The file to upload
     * @param description The document description
     * @return The uploaded document details
     */
    @PostMapping("/upload/{userId}")
    public ResponseEntity<?> uploadDocument(
            @PathVariable String userId,
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "description", required = false) String description) {
        
        Optional<VendorDetails> vendorOpt = vendorDetailsRepository.findByUserId(userId);
        if (vendorOpt.isEmpty()) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("message", "Vendor not found for user ID: " + userId));
        }
        
        VendorDocument document = vendorDocumentService.saveDocument(file, vendorOpt.get().getId(), description);
        
        // Create response with download URL
        Map<String, Object> response = new HashMap<>();
        response.put("id", document.getId());
        response.put("fileName", document.getFileName());
        response.put("originalFileName", document.getOriginalFileName());
        response.put("fileType", document.getFileType());
        response.put("fileSize", document.getFileSize());
        response.put("description", document.getDescription());
        response.put("uploadDate", document.getUploadDate());
        
        // Create download URL
        String fileDownloadUri = ServletUriComponentsBuilder.fromCurrentContextPath()
                .path("/api/files/download/")
                .path(document.getFileName())
                .toUriString();
        response.put("fileDownloadUri", fileDownloadUri);
        
        // Create view URL
        String fileViewUri = ServletUriComponentsBuilder.fromCurrentContextPath()
                .path("/api/files/view/")
                .path(document.getFileName())
                .toUriString();
        response.put("fileViewUri", fileViewUri);
        
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    /**
     * Delete a document
     *
     * @param documentId The document ID
     * @return Response with deletion status
     */
    @DeleteMapping("/{documentId}")
    public ResponseEntity<?> deleteDocument(@PathVariable Long documentId) {
        boolean deleted = vendorDocumentService.deleteDocument(documentId);
        
        if (deleted) {
            return ResponseEntity.ok(Map.of("deleted", true));
        } else {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("message", "Document not found with ID: " + documentId));
        }
    }
}
