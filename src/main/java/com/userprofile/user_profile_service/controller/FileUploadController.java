package com.userprofile.user_profile_service.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import com.userprofile.user_profile_service.service.FileStorageService;

import jakarta.servlet.http.HttpServletRequest;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/files")
@CrossOrigin(origins = { "http://localhost:5173", "http://localhost:3000", "http://localhost:8081" })
public class FileUploadController {

    @Autowired
    private FileStorageService fileStorageService;

    /**
     * Simple ping endpoint to test connectivity
     *
     * @param request The HTTP request
     * @return A simple response to confirm the API is accessible
     */
    @GetMapping("/ping")
    public ResponseEntity<String> ping(HttpServletRequest request) {
        System.out.println("Ping request received");

        // Get the origin from the request
        String origin = request.getHeader("Origin");
        System.out.println("Request origin: " + origin);

        // Let the global CORS configuration handle the CORS headers
        return ResponseEntity.ok()
                .body("API is accessible");
    }

    /**
     * Upload a file
     *
     * @param file     The file to upload
     * @param fileType The type of file (e.g., "profile", "logo", "certificate")
     * @return Response with file details
     */
    @PostMapping("/upload")
    public ResponseEntity<Map<String, String>> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "fileType", defaultValue = "document") String fileType) {

        System.out.println("File upload request received for file type: " + fileType);
        System.out.println("Original filename: " + file.getOriginalFilename());
        System.out.println("Content type: " + file.getContentType());

        String fileName = fileStorageService.storeFile(file, fileType);
        System.out.println("File stored with name: " + fileName);

        // Create file download URL - ensure this is the correct base URL
        String fileDownloadUri = ServletUriComponentsBuilder.fromCurrentContextPath()
                .path("/api/files/download/")
                .path(fileName)
                .toUriString();

        Map<String, String> response = new HashMap<>();
        response.put("fileName", fileName);
        response.put("fileDownloadUri", fileDownloadUri);
        response.put("fileType", file.getContentType());
        response.put("size", String.valueOf(file.getSize()));
        response.put("originalFileName", file.getOriginalFilename());
        response.put("uploadType", fileType);

        System.out.println("Returning response: " + response);
        return ResponseEntity.ok(response);
    }

    /**
     * Download a file
     *
     * @param fileName The name of the file to download
     * @param request  The HTTP request
     * @return The file as a Resource
     */
    @GetMapping("/download/{fileName:.+}")
    public ResponseEntity<Resource> downloadFile(@PathVariable String fileName, HttpServletRequest request) {
        System.out.println("Download file request received for: " + fileName);

        try {
            // Load file as Resource
            Resource resource = fileStorageService.loadFileAsResource(fileName);
            System.out.println("File resource loaded: " + resource.getFilename());
            System.out.println("File exists: " + resource.exists());
            System.out.println("File size: " + resource.contentLength() + " bytes");

            // Try to determine file's content type
            String contentType = null;
            try {
                contentType = request.getServletContext().getMimeType(resource.getFile().getAbsolutePath());
                System.out.println("Content type from servlet context: " + contentType);
            } catch (IOException ex) {
                // Logger would be used here in a production environment
                System.out.println("Could not determine file type: " + ex.getMessage());
                ex.printStackTrace();
            }

            // Fallback to the default content type if type could not be determined
            if (contentType == null) {
                contentType = "application/octet-stream";
                System.out.println("Using fallback content type: " + contentType);
            }

            System.out.println("Returning file with attachment disposition");
            // Get the origin from the request
            String origin = request.getHeader("Origin");
            System.out.println("Request origin: " + origin);

            // Let the global CORS configuration handle the CORS headers
            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + resource.getFilename() + "\"")
                    .header(HttpHeaders.CACHE_CONTROL, "max-age=3600") // Add caching header
                    .body(resource);
        } catch (Exception e) {
            System.out.println("Error serving file for download: " + e.getMessage());
            e.printStackTrace();
            // Get the origin from the request
            String origin = request.getHeader("Origin");
            System.out.println("Request origin (error): " + origin);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(null);
        }
    }

    /**
     * View a file in the browser
     *
     * @param fileName The name of the file to view
     * @param request  The HTTP request
     * @return The file as a Resource
     */
    @GetMapping("/view/{fileName:.+}")
    public ResponseEntity<Resource> viewFile(@PathVariable String fileName, HttpServletRequest request) {
        System.out.println("View file request received for: " + fileName);

        try {
            // Load file as Resource
            Resource resource = fileStorageService.loadFileAsResource(fileName);
            System.out.println("File resource loaded: " + resource.getFilename());
            System.out.println("File exists: " + resource.exists());
            System.out.println("File size: " + resource.contentLength() + " bytes");

            // Try to determine file's content type
            String contentType = null;
            try {
                contentType = request.getServletContext().getMimeType(resource.getFile().getAbsolutePath());
                System.out.println("Content type from servlet context: " + contentType);

                // For better file type detection
                String fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
                System.out.println("File extension: " + fileExtension);

                // Set specific content types for common file types
                switch (fileExtension) {
                    case "pdf":
                        contentType = "application/pdf";
                        break;
                    case "jpg":
                    case "jpeg":
                        contentType = "image/jpeg";
                        break;
                    case "png":
                        contentType = "image/png";
                        break;
                    case "gif":
                        contentType = "image/gif";
                        break;
                    case "svg":
                        contentType = "image/svg+xml";
                        break;
                    case "webp":
                        contentType = "image/webp";
                        break;
                    case "doc":
                        contentType = "application/msword";
                        break;
                    case "docx":
                        contentType = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
                        break;
                    case "xls":
                        contentType = "application/vnd.ms-excel";
                        break;
                    case "xlsx":
                        contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                        break;
                    case "csv":
                        contentType = "text/csv";
                        break;
                    case "txt":
                        contentType = "text/plain";
                        break;
                    case "html":
                    case "htm":
                        contentType = "text/html";
                        break;
                    case "json":
                        contentType = "application/json";
                        break;
                    case "xml":
                        contentType = "application/xml";
                        break;
                }

                System.out.println("Final content type: " + contentType);

            } catch (IOException ex) {
                System.out.println("Could not determine file type: " + ex.getMessage());
                ex.printStackTrace();
            }

            // Fallback to the default content type if type could not be determined
            if (contentType == null) {
                contentType = "application/octet-stream";
                System.out.println("Using fallback content type: " + contentType);
            }

            // Use inline content disposition to display in browser instead of downloading
            System.out.println("Returning file with inline disposition");
            // Get the origin from the request
            String origin = request.getHeader("Origin");
            System.out.println("Request origin: " + origin);

            // Let the global CORS configuration handle the CORS headers
            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + resource.getFilename() + "\"")
                    .header(HttpHeaders.CACHE_CONTROL, "max-age=3600") // Add caching header
                    .body(resource);
        } catch (Exception e) {
            System.out.println("Error serving file: " + e.getMessage());
            e.printStackTrace();
            // Get the origin from the request
            String origin = request.getHeader("Origin");
            System.out.println("Request origin (error): " + origin);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(null);
        }
    }

    /**
     * Delete a file
     *
     * @param fileName The name of the file to delete
     * @return Response with deletion status
     */
    @DeleteMapping("/delete/{fileName:.+}")
    public ResponseEntity<Map<String, Boolean>> deleteFile(@PathVariable String fileName) {
        boolean deleted = fileStorageService.deleteFile(fileName);

        Map<String, Boolean> response = new HashMap<>();
        response.put("deleted", deleted);

        return ResponseEntity.ok(response);
    }
}
