package com.userprofile.user_profile_service.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.userprofile.user_profile_service.entity.CustomerDetails;
import com.userprofile.user_profile_service.entity.VendorDetails;
import com.userprofile.user_profile_service.service.ProfileService;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * REST controller for user-centric profile operations.
 * This controller focuses on operations that span multiple profile types
 * or provide a unified interface for profile management.
 */
@RestController
@RequestMapping("/api/profiles")
@CrossOrigin(origins = { "http://localhost:5173", "http://localhost:3000", "http://localhost:8081" })
public class ProfileController {

    @Autowired
    private ProfileService profileService;

    /**
     * Check if a profile exists for the given user ID
     *
     * @param userId The user ID to check
     * @return Response with profile existence status
     */
    @GetMapping("/exists/{userId}")
    public ResponseEntity<Map<String, Object>> checkProfileExists(@PathVariable String userId) {
        System.out.println("ProfileController: Checking profile existence for user ID: " + userId);

        Map<String, Object> response = new HashMap<>();

        try {
            boolean customerExists = profileService.customerProfileExists(userId);
            boolean vendorExists = profileService.vendorProfileExists(userId);

            response.put("customerProfileExists", customerExists);
            response.put("vendorProfileExists", vendorExists);
            response.put("anyProfileExists", customerExists || vendorExists);
            response.put("userId", userId);

            System.out.println("ProfileController: Profile check results - Customer: " + customerExists + ", Vendor: " + vendorExists);

            return ResponseEntity.ok(response);
        } catch (Exception ex) {
            System.err.println("ProfileController: Error checking profile existence for user ID: " + userId + " - " + ex.getMessage());
            ex.printStackTrace();

            response.put("error", "Error checking profile existence");
            response.put("message", ex.getMessage());
            response.put("userId", userId);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Get customer profile by user ID - Redirects to CustomerController
     * Maintained for backward compatibility
     *
     * @param userId The user ID
     * @return The customer profile if found
     */
    @GetMapping("/customer/{userId}")
    public ResponseEntity<?> getCustomerProfile(@PathVariable String userId) {
        Optional<CustomerDetails> optionalProfile = profileService.getCustomerDetailsByUserId(userId);
        if (optionalProfile.isPresent()) {
            return ResponseEntity.ok(optionalProfile.get());
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Get vendor profile by user ID - Redirects to VendorController
     * Maintained for backward compatibility
     *
     * @param userId The user ID
     * @return The vendor profile if found
     */
    @GetMapping("/vendor/{userId}")
    public ResponseEntity<?> getVendorProfile(@PathVariable String userId) {
        System.out.println("ProfileController: Getting vendor profile for user ID: " + userId);

        try {
            Optional<VendorDetails> optionalProfile = profileService.getVendorDetailsByUserId(userId);

            if (optionalProfile.isPresent()) {
                System.out.println("ProfileController: Vendor profile found for user ID: " + userId);
                return ResponseEntity.ok(optionalProfile.get());
            } else {
                System.out.println("ProfileController: Vendor profile NOT found for user ID: " + userId);
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(Map.of(
                            "status", "error",
                            "message", "Vendor profile not found for user ID: " + userId,
                            "code", HttpStatus.NOT_FOUND.value(),
                            "userId", userId
                        ));
            }
        } catch (Exception ex) {
            System.err.println("ProfileController: Error getting vendor profile for user ID: " + userId + " - " + ex.getMessage());
            ex.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of(
                        "status", "error",
                        "message", "Internal server error while retrieving vendor profile",
                        "code", HttpStatus.INTERNAL_SERVER_ERROR.value(),
                        "userId", userId
                    ));
        }
    }

    /**
     * Create or update customer profile - Redirects to CustomerController
     * Maintained for backward compatibility
     *
     * @param customerDetails The customer details to save
     * @return The saved customer details
     */
    @PostMapping("/customer")
    public ResponseEntity<CustomerDetails> saveCustomerProfile(@RequestBody CustomerDetails customerDetails) {
        CustomerDetails savedProfile = profileService.saveCustomerProfile(customerDetails);
        return ResponseEntity.status(HttpStatus.CREATED).body(savedProfile);
    }

    /**
     * Create or update vendor profile - Redirects to VendorController
     * Maintained for backward compatibility
     *
     * @param vendorDetails The vendor details to save
     * @return The saved vendor details
     */
    @PostMapping("/vendor")
    public ResponseEntity<VendorDetails> saveVendorProfile(@RequestBody VendorDetails vendorDetails) {
        VendorDetails savedProfile = profileService.saveVendorProfile(vendorDetails);
        return ResponseEntity.status(HttpStatus.CREATED).body(savedProfile);
    }

    /**
     * Update customer profile by user ID - Maintained for backward compatibility
     *
     * @param userId          The user ID
     * @param customerDetails The customer details to update
     * @return The updated customer details
     */
    @PutMapping("/customer/{userId}")
    public ResponseEntity<?> updateCustomerProfile(@PathVariable String userId,
            @RequestBody CustomerDetails customerDetails) {
        Optional<CustomerDetails> optionalCustomer = profileService.getCustomerDetailsByUserId(userId);

        if (optionalCustomer.isPresent()) {
            CustomerDetails existingCustomer = optionalCustomer.get();
            customerDetails.setId(existingCustomer.getId());
            CustomerDetails updatedCustomer = profileService.saveCustomerProfile(customerDetails);
            return ResponseEntity.ok(updatedCustomer);
        } else {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("message", "Customer profile not found for user ID: " + userId));
        }
    }

    /**
     * Update vendor profile by user ID - Maintained for backward compatibility
     *
     * @param userId        The user ID
     * @param vendorDetails The vendor details to update
     * @return The updated vendor details
     */
    @PutMapping("/vendor/{userId}")
    public ResponseEntity<?> updateVendorProfile(@PathVariable String userId,
            @RequestBody VendorDetails vendorDetails) {
        Optional<VendorDetails> optionalVendor = profileService.getVendorDetailsByUserId(userId);

        if (optionalVendor.isPresent()) {
            VendorDetails existingVendor = optionalVendor.get();
            vendorDetails.setId(existingVendor.getId());
            VendorDetails updatedVendor = profileService.saveVendorProfile(vendorDetails);
            return ResponseEntity.ok(updatedVendor);
        } else {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("message", "Vendor profile not found for user ID: " + userId));
        }
    }

    /**
     * Debug endpoint to check database status and create test vendor if needed
     *
     * @param userId The user ID to check/create
     * @return Debug information
     */
    @GetMapping("/debug/vendor/{userId}")
    public ResponseEntity<?> debugVendorProfile(@PathVariable String userId) {
        Map<String, Object> debugInfo = new HashMap<>();
        debugInfo.put("requestedUserId", userId);
        debugInfo.put("timestamp", java.time.LocalDateTime.now());

        try {
            // Check if vendor exists
            Optional<VendorDetails> existingVendor = profileService.getVendorDetailsByUserId(userId);
            debugInfo.put("vendorExists", existingVendor.isPresent());

            if (existingVendor.isPresent()) {
                VendorDetails vendor = existingVendor.get();
                debugInfo.put("vendorId", vendor.getId());
                debugInfo.put("businessName", vendor.getBusinessName());
                debugInfo.put("profileComplete", vendor.isProfileComplete());
            } else {
                // Create a test vendor profile
                VendorDetails testVendor = new VendorDetails();
                testVendor.setUserId(userId);
                testVendor.setBusinessName("Test Business for " + userId);
                testVendor.setBusinessEmail("<EMAIL>");
                testVendor.setBusinessPhone("+1234567890");
                testVendor.setBusinessCategory("Technology");
                testVendor.setProfileComplete(false);

                VendorDetails savedVendor = profileService.saveVendorProfile(testVendor);
                debugInfo.put("testVendorCreated", true);
                debugInfo.put("createdVendorId", savedVendor.getId());
                debugInfo.put("message", "Test vendor profile created for debugging");
            }

            return ResponseEntity.ok(debugInfo);

        } catch (Exception ex) {
            debugInfo.put("error", ex.getMessage());
            debugInfo.put("stackTrace", ex.getStackTrace());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(debugInfo);
        }
    }

    /**
     * Health check endpoint to verify API and database connectivity
     *
     * @return Health status information
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("timestamp", java.time.LocalDateTime.now());
        health.put("service", "user-profile-service");

        try {
            // Test database connectivity by counting records
            long customerCount = profileService.getCustomerCount();
            long vendorCount = profileService.getVendorCount();

            health.put("database", "CONNECTED");
            health.put("customerCount", customerCount);
            health.put("vendorCount", vendorCount);

            return ResponseEntity.ok(health);
        } catch (Exception ex) {
            health.put("status", "DOWN");
            health.put("database", "DISCONNECTED");
            health.put("error", ex.getMessage());

            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(health);
        }
    }
}
