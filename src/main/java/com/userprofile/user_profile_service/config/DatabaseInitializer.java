package com.userprofile.user_profile_service.config;

import javax.sql.DataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

/**
 * Component to initialize database tables if they don't exist.
 * This runs after Hibernate's schema creation but provides additional checks
 * and logging to ensure tables are properly created.
 */
@Component
public class DatabaseInitializer implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(DatabaseInitializer.class);

    @Autowired
    private DataSource dataSource;

    @Override
    public void run(String... args) throws Exception {
        try {
            // Log database connection information
            logger.info("Attempting to connect to database...");
            logger.info("Database URL: {}", dataSource.getConnection().getMetaData().getURL());
            logger.info("Database Product Name: {}", dataSource.getConnection().getMetaData().getDatabaseProductName());
            logger.info("Database Product Version: {}",
                    dataSource.getConnection().getMetaData().getDatabaseProductVersion());
            logger.info("Database Driver Name: {}", dataSource.getConnection().getMetaData().getDriverName());
            logger.info("Database Driver Version: {}", dataSource.getConnection().getMetaData().getDriverVersion());
            logger.info("Database Username: {}", dataSource.getConnection().getMetaData().getUserName());
            logger.info("Database connected successfully!");

            // List all tables in the database
            logger.info("Listing all tables in the database:");
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
            jdbcTemplate.query(
                    "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'",
                    (rs, rowNum) -> {
                        logger.info("Table found: {}", rs.getString("table_name"));
                        return null;
                    });

            // Check if CustomerDetails table exists
            boolean customerTableExists = tableExists(jdbcTemplate, "customer_details");
            if (customerTableExists) {
                logger.info("CustomerDetails table already exists");
                // Count rows in the table
                Integer customerCount = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM customer_details",
                        Integer.class);
                logger.info("CustomerDetails table has {} rows", customerCount);
            } else {
                logger.info("CustomerDetails table does not exist, it will be created by Hibernate");
                createCustomerDetailsTable(jdbcTemplate);
            }

            // Check if VendorDetails table exists
            boolean vendorTableExists = tableExists(jdbcTemplate, "vendor_details");
            if (vendorTableExists) {
                logger.info("VendorDetails table already exists");
                // Count rows in the table
                Integer vendorCount = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM vendor_details", Integer.class);
                logger.info("VendorDetails table has {} rows", vendorCount);
            } else {
                logger.info("VendorDetails table does not exist, it will be created by Hibernate");
                createVendorDetailsTable(jdbcTemplate);
            }

            // Check if VendorDocuments table exists
            boolean vendorDocumentsTableExists = tableExists(jdbcTemplate, "vendor_documents");
            if (vendorDocumentsTableExists) {
                logger.info("VendorDocuments table already exists");
                // Count rows in the table
                Integer documentsCount = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM vendor_documents",
                        Integer.class);
                logger.info("VendorDocuments table has {} rows", documentsCount);
            } else {
                logger.info("VendorDocuments table does not exist, it will be created by Hibernate");
                createVendorDocumentsTable(jdbcTemplate);
            }
        } catch (Exception e) {
            logger.error("Error initializing database", e);
            throw e;
        }
    }

    private boolean tableExists(JdbcTemplate jdbcTemplate, String tableName) {
        try {
            String sql = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = ?";
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, tableName.toUpperCase());
            return count != null && count > 0;
        } catch (Exception e) {
            logger.error("Error checking if table exists: " + tableName, e);
            return false;
        }
    }

    private void createCustomerDetailsTable(JdbcTemplate jdbcTemplate) {
        try {
            String sql = "CREATE TABLE IF NOT EXISTS customer_details (" +
                    "id BIGSERIAL PRIMARY KEY, " +
                    "user_id VARCHAR(255) UNIQUE, " +
                    "first_name VARCHAR(255), " +
                    "last_name VARCHAR(255), " +
                    "email_address VARCHAR(255), " +
                    "phone_number VARCHAR(255), " +
                    "date_of_birth DATE, " +
                    "profile_picture VARCHAR(255), " +
                    "primary_street_address VARCHAR(255), " +
                    "primary_city VARCHAR(255), " +
                    "primary_state VARCHAR(255), " +
                    "primary_zip_code VARCHAR(255), " +
                    "primary_country VARCHAR(255), " +
                    "secondary_street_address VARCHAR(255), " +
                    "secondary_city VARCHAR(255), " +
                    "secondary_state VARCHAR(255), " +
                    "secondary_zip_code VARCHAR(255), " +
                    "secondary_country VARCHAR(255), " +
                    "designation_or_role VARCHAR(255), " +
                    "industry_or_domain VARCHAR(255), " +
                    "company_name VARCHAR(255), " +
                    "preferred_vendor_type VARCHAR(255), " +
                    "subcategories_of_interest VARCHAR(255), " +
                    "categories_of_services_needed VARCHAR(255), " +
                    "receive_notifications_from_vendors BOOLEAN, " +
                    "visible_to_vendors BOOLEAN, " +
                    "profile_complete BOOLEAN" +
                    ")";
            jdbcTemplate.execute(sql);
            logger.info("CustomerDetails table created successfully");
        } catch (Exception e) {
            logger.error("Error creating CustomerDetails table", e);
        }
    }

    private void createVendorDetailsTable(JdbcTemplate jdbcTemplate) {
        try {
            String sql = "CREATE TABLE IF NOT EXISTS vendor_details (" +
                    "id BIGSERIAL PRIMARY KEY, " +
                    "user_id VARCHAR(255) UNIQUE, " +
                    "business_name VARCHAR(255), " +
                    "business_logo VARCHAR(255), " +
                    "business_email VARCHAR(255), " +
                    "business_phone VARCHAR(255), " +
                    "street_address VARCHAR(255), " +
                    "city VARCHAR(255), " +
                    "state VARCHAR(255), " +
                    "zip_code VARCHAR(255), " +
                    "country VARCHAR(255), " +
                    "business_category VARCHAR(255), " +
                    "business_subcategory VARCHAR(255), " +
                    "business_website VARCHAR(255), " +
                    "years_of_experience VARCHAR(255), " +
                    "certificate VARCHAR(255), " +
                    "product_name VARCHAR(255), " +
                    "product_description VARCHAR(255), " +
                    "product_category VARCHAR(255), " +
                    "registration_number VARCHAR(255), " +
                    "regions_supported VARCHAR(255), " +
                    "license_details VARCHAR(255), " +
                    "profile_complete BOOLEAN" +
                    ")";
            jdbcTemplate.execute(sql);
            logger.info("VendorDetails table created successfully");
        } catch (Exception e) {
            logger.error("Error creating VendorDetails table", e);
        }
    }

    private void createVendorDocumentsTable(JdbcTemplate jdbcTemplate) {
        try {
            String sql = "CREATE TABLE IF NOT EXISTS vendor_documents (" +
                    "id BIGSERIAL PRIMARY KEY, " +
                    "vendor_id BIGINT NOT NULL, " +
                    "file_name VARCHAR(255) NOT NULL, " +
                    "original_file_name VARCHAR(255), " +
                    "file_type VARCHAR(255), " +
                    "file_size BIGINT, " +
                    "description VARCHAR(500), " +
                    "upload_date TIMESTAMP NOT NULL, " +
                    "FOREIGN KEY (vendor_id) REFERENCES vendor_details(id) ON DELETE CASCADE" +
                    ")";
            jdbcTemplate.execute(sql);
            logger.info("VendorDocuments table created successfully");
        } catch (Exception e) {
            logger.error("Error creating VendorDocuments table", e);
        }
    }
}
