package com.userprofile.user_profile_service.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import java.util.Optional;

import com.userprofile.user_profile_service.entity.CustomerDetails;


public interface CustomerDetailsRepository extends JpaRepository<CustomerDetails, Long> {

    /**
     * Find customer details by user ID
     *
     * @param userId The unique identifier for the user
     * @return Optional containing customer details if found
     */
    Optional<CustomerDetails> findByUserId(String userId);
}