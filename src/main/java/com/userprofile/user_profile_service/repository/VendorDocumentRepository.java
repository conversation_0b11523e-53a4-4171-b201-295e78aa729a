package com.userprofile.user_profile_service.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import java.util.List;
import com.userprofile.user_profile_service.entity.VendorDocument;

/**
 * Repository interface for VendorDocument entity
 */
public interface VendorDocumentRepository extends JpaRepository<VendorDocument, Long> {
    
    /**
     * Find all documents for a specific vendor
     * 
     * @param vendorId The vendor ID
     * @return List of vendor documents
     */
    List<VendorDocument> findByVendorId(Long vendorId);
    
    /**
     * Delete all documents for a specific vendor
     * 
     * @param vendorId The vendor ID
     */
    void deleteByVendorId(Long vendorId);
}
