package com.userprofile.user_profile_service.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import java.util.Optional;
import com.userprofile.user_profile_service.entity.VendorDetails;

public interface VendorDetailsRepository extends JpaRepository<VendorDetails, Long> {

    /**
     * Find vendor details by user ID
     *
     * @param userId The unique identifier for the user
     * @return Optional containing vendor details if found
     */
    Optional<VendorDetails> findByUserId(String userId);
}