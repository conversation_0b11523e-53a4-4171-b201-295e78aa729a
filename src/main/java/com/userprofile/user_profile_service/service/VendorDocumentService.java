package com.userprofile.user_profile_service.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.userprofile.user_profile_service.entity.VendorDetails;
import com.userprofile.user_profile_service.entity.VendorDocument;
import com.userprofile.user_profile_service.repository.VendorDetailsRepository;
import com.userprofile.user_profile_service.repository.VendorDocumentRepository;

import java.util.List;
import java.util.Optional;

/**
 * Service class for managing vendor documents
 */
@Service
public class VendorDocumentService {

    @Autowired
    private VendorDocumentRepository vendorDocumentRepository;

    @Autowired
    private VendorDetailsRepository vendorDetailsRepository;

    @Autowired
    private FileStorageService fileStorageService;

    /**
     * Get all documents for a vendor by vendor ID
     *
     * @param vendorId The vendor ID
     * @return List of vendor documents
     */
    public List<VendorDocument> getDocumentsByVendorId(Long vendorId) {
        return vendorDocumentRepository.findByVendorId(vendorId);
    }

    /**
     * Get all documents for a vendor by user ID
     *
     * @param userId The user ID
     * @return List of vendor documents
     */
    public List<VendorDocument> getDocumentsByUserId(String userId) {
        Optional<VendorDetails> vendorOpt = vendorDetailsRepository.findByUserId(userId);
        if (vendorOpt.isPresent()) {
            return vendorDocumentRepository.findByVendorId(vendorOpt.get().getId());
        }
        return List.of();
    }

    /**
     * Save a document for a vendor
     *
     * @param file The file to save
     * @param vendorId The vendor ID
     * @param description The document description
     * @return The saved document
     */
    public VendorDocument saveDocument(MultipartFile file, Long vendorId, String description) {
        // Store the file using FileStorageService
        String fileName = fileStorageService.storeFile(file, "vendor_document");
        
        // Create and save the document metadata
        VendorDocument document = new VendorDocument();
        document.setVendorId(vendorId);
        document.setFileName(fileName);
        document.setOriginalFileName(file.getOriginalFilename());
        document.setFileType(file.getContentType());
        document.setFileSize(file.getSize());
        document.setDescription(description);
        
        return vendorDocumentRepository.save(document);
    }

    /**
     * Delete a document by ID
     *
     * @param documentId The document ID
     * @return true if deleted, false otherwise
     */
    @Transactional
    public boolean deleteDocument(Long documentId) {
        Optional<VendorDocument> documentOpt = vendorDocumentRepository.findById(documentId);
        if (documentOpt.isPresent()) {
            VendorDocument document = documentOpt.get();
            // Delete the file from storage
            fileStorageService.deleteFile(document.getFileName());
            // Delete the document metadata
            vendorDocumentRepository.deleteById(documentId);
            return true;
        }
        return false;
    }
}
