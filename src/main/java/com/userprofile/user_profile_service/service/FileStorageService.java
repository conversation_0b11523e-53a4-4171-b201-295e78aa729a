package com.userprofile.user_profile_service.service;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.UUID;

@Service
public class FileStorageService {

    private final Path fileStorageLocation;

    public FileStorageService(@Value("${file.upload-dir:uploads}") String uploadDir) {
        this.fileStorageLocation = Paths.get(uploadDir)
                .toAbsolutePath().normalize();

        try {
            Files.createDirectories(this.fileStorageLocation);
        } catch (Exception ex) {
            throw new RuntimeException("Could not create the directory where the uploaded files will be stored.", ex);
        }
    }

    /**
     * Store a file with a unique name
     *
     * @param file     The file to store
     * @param fileType The type of file (e.g., "profile", "logo", "certificate")
     * @return The filename of the stored file
     */
    public String storeFile(MultipartFile file, String fileType) {
        // Normalize file name
        String originalFileName = StringUtils.cleanPath(file.getOriginalFilename());

        try {
            // Check if the file's name contains invalid characters
            if (originalFileName.contains("..")) {
                throw new RuntimeException("Filename contains invalid path sequence " + originalFileName);
            }

            // Log file information for debugging
            System.out.println("Storing file: " + originalFileName);
            System.out.println("File type: " + fileType);
            System.out.println("Content type: " + file.getContentType());
            System.out.println("File size: " + file.getSize() + " bytes");

            // Generate a unique file name to prevent overwriting
            String fileExtension = getFileExtension(originalFileName);
            String newFileName = fileType + "_" + UUID.randomUUID().toString() + fileExtension;

            // Copy file to the target location
            Path targetLocation = this.fileStorageLocation.resolve(newFileName);
            Files.copy(file.getInputStream(), targetLocation, StandardCopyOption.REPLACE_EXISTING);

            System.out.println("File stored successfully: " + newFileName);
            return newFileName;
        } catch (IOException ex) {
            System.err.println("Error storing file: " + ex.getMessage());
            ex.printStackTrace();
            throw new RuntimeException("Could not store file " + originalFileName + ". Please try again!", ex);
        }
    }

    /**
     * Load a file as a Resource
     *
     * @param fileName The name of the file to load
     * @return The file as a Resource
     */
    public Resource loadFileAsResource(String fileName) {
        try {
            System.out.println("Loading file as resource: " + fileName);
            System.out.println("File storage location: " + this.fileStorageLocation.toString());

            Path filePath = this.fileStorageLocation.resolve(fileName).normalize();
            System.out.println("Resolved file path: " + filePath.toString());
            System.out.println("File exists on disk: " + Files.exists(filePath));

            if (Files.exists(filePath)) {
                System.out.println("File size on disk: " + Files.size(filePath) + " bytes");
                System.out.println("File URI: " + filePath.toUri().toString());
            }

            Resource resource = new UrlResource(filePath.toUri());
            System.out.println("Resource created: " + resource.toString());
            System.out.println("Resource exists: " + resource.exists());

            if (resource.exists()) {
                System.out.println("Resource file name: " + resource.getFilename());
                System.out.println("Resource URI: " + resource.getURI().toString());
                return resource;
            } else {
                System.out.println("Resource does not exist");
                throw new RuntimeException("File not found " + fileName);
            }
        } catch (MalformedURLException ex) {
            System.out.println("MalformedURLException: " + ex.getMessage());
            ex.printStackTrace();
            throw new RuntimeException("File not found " + fileName, ex);
        } catch (IOException ex) {
            System.out.println("IOException: " + ex.getMessage());
            ex.printStackTrace();
            throw new RuntimeException("Error accessing file " + fileName, ex);
        }
    }

    /**
     * Delete a file
     *
     * @param fileName The name of the file to delete
     * @return true if the file was deleted, false otherwise
     */
    public boolean deleteFile(String fileName) {
        try {
            Path filePath = this.fileStorageLocation.resolve(fileName).normalize();
            return Files.deleteIfExists(filePath);
        } catch (IOException ex) {
            throw new RuntimeException("Error deleting file " + fileName, ex);
        }
    }

    /**
     * Get the file extension from a filename
     *
     * @param fileName The filename
     * @return The file extension (including the dot)
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.lastIndexOf(".") == -1) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf("."));
    }
}
