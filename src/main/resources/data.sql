-- Insert sample vendor data for testing
-- This will be executed after schema creation

-- Insert a test vendor profile
INSERT INTO vendor_details (
    user_id, 
    business_name, 
    business_email, 
    business_phone, 
    business_category, 
    business_subcategory,
    business_website,
    years_of_experience,
    street_address,
    city,
    state,
    zip_code,
    country,
    product_name,
    product_description,
    product_category,
    registration_number,
    regions_supported,
    license_details,
    profile_complete
) VALUES (
    '056fe13d-e15b-4166-91d3-67833810fc0e',
    'Tech Solutions Inc',
    '<EMAIL>',
    '******-0123',
    'Technology',
    'Software Development',
    'https://www.techsolutions.com',
    '5',
    '123 Tech Street',
    'San Francisco',
    'CA',
    '94105',
    'USA',
    'Cloud Management Platform',
    'Enterprise-grade cloud management and monitoring solution',
    'SaaS',
    'REG-2024-001',
    'North America, Europe',
    'Enterprise Software License',
    true
) ON CONFLICT (user_id) DO UPDATE SET
    business_name = EXCLUDED.business_name,
    business_email = EXCLUDED.business_email,
    business_phone = EXCLUDED.business_phone,
    business_category = EXCLUDED.business_category,
    business_subcategory = EXCLUDED.business_subcategory,
    business_website = EXCLUDED.business_website,
    years_of_experience = EXCLUDED.years_of_experience,
    street_address = EXCLUDED.street_address,
    city = EXCLUDED.city,
    state = EXCLUDED.state,
    zip_code = EXCLUDED.zip_code,
    country = EXCLUDED.country,
    product_name = EXCLUDED.product_name,
    product_description = EXCLUDED.product_description,
    product_category = EXCLUDED.product_category,
    registration_number = EXCLUDED.registration_number,
    regions_supported = EXCLUDED.regions_supported,
    license_details = EXCLUDED.license_details,
    profile_complete = EXCLUDED.profile_complete;

-- Insert another test vendor
INSERT INTO vendor_details (
    user_id, 
    business_name, 
    business_email, 
    business_phone, 
    business_category, 
    profile_complete
) VALUES (
    'test-vendor-123',
    'Sample Vendor Co',
    '<EMAIL>',
    '******-0456',
    'Manufacturing',
    false
) ON CONFLICT (user_id) DO NOTHING;

-- Insert a test customer profile
INSERT INTO customer_details (
    user_id,
    first_name,
    last_name,
    full_name,
    email_address,
    phone_number,
    company_name,
    industry_or_domain,
    profile_complete
) VALUES (
    'test-customer-456',
    'John',
    'Doe',
    'John Doe',
    '<EMAIL>',
    '******-0789',
    'Example Corp',
    'Technology',
    true
) ON CONFLICT (user_id) DO NOTHING;
