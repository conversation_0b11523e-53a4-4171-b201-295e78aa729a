spring.application.name=user-profile-service
spring.docker.compose.enabled=false

# PostgreSQL Configuration
spring.datasource.url=**************************************************************
spring.datasource.username=postgres
spring.datasource.password=postgres

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Database Initialization
spring.sql.init.mode=always
spring.sql.init.schema-locations=classpath:schema.sql
spring.sql.init.data-locations=classpath:data.sql

# File Upload Configuration
spring.servlet.multipart.enabled=true
spring.servlet.multipart.file-size-threshold=2KB
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=15MB

# File Storage Location
file.upload-dir=./uploads

springdoc.api-docs.enabled=true
springdoc.swagger-ui.enabled=true
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/

# Keycloak Configuration
spring.security.enabled=false
# spring.security.oauth2.client.provider.keycloak.issuer-uri=http://localhost:8085/realms/b2b-realm
# spring.security.oauth2.client.registration.keycloak.client-id=b2b-rest-api
# spring.security.oauth2.client.registration.keycloak.client-secret=uCkCK5O8XRXAm7bG9GX881ruZI28o9XD
# spring.security.oauth2.client.registration.keycloak.scope=openid

# Change static resource path to avoid conflict with REST endpoints
spring.mvc.static-path-pattern=/static/**
spring.web.resources.add-mappings=true

#port 
server.port=8080


