-- This script will be executed by Spring Boot on startup if spring.sql.init.mode=always
-- Currently it's not enabled by default, but can be used for manual database initialization

-- Create customer_details table if it doesn't exist
CREATE TABLE IF NOT EXISTS customer_details (
    id BIGSERIAL PRIMARY KEY,
    user_id VARCHAR(255) UNIQUE,
    first_name <PERSON><PERSON><PERSON><PERSON>(255),
    last_name <PERSON><PERSON><PERSON><PERSON>(255),
    full_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    email_address VARCHAR(255),
    phone_number VA<PERSON>HAR(255),
    date_of_birth DATE,
    profile_picture VARCHAR(255),
    primary_street_address VARCHAR(255),
    primary_city VARCHAR(255),
    primary_state VARCHAR(255),
    primary_zip_code VARCHAR(255),
    primary_country VARCHAR(255),
    secondary_street_address VARCHAR(255),
    secondary_city VARCHAR(255),
    secondary_state VARCHAR(255),
    secondary_zip_code VARCHAR(255),
    secondary_country VARCHAR(255),
    designation_or_role VARCHAR(255),
    industry_or_domain VARCHAR(255),
    company_name VA<PERSON><PERSON><PERSON>(255),
    preferred_vendor_type VARCHAR(255),
    subcategories_of_interest VARCHAR(255),
    categories_of_services_needed VARCHAR(255),
    receive_notifications_from_vendors BOOLEAN,
    visible_to_vendors BOOLEAN,
    profile_complete BOOLEAN
);

-- Create vendor_details table if it doesn't exist
CREATE TABLE IF NOT EXISTS vendor_details (
    id BIGSERIAL PRIMARY KEY,
    user_id VARCHAR(255) UNIQUE,
    business_name VARCHAR(255),
    business_logo VARCHAR(255),
    business_email VARCHAR(255),
    business_phone VARCHAR(255),
    street_address VARCHAR(255),
    city VARCHAR(255),
    state VARCHAR(255),
    zip_code VARCHAR(255),
    country VARCHAR(255),
    business_category VARCHAR(255),
    business_subcategory VARCHAR(255),
    business_website VARCHAR(255),
    years_of_experience VARCHAR(255),
    certificate VARCHAR(255),
    product_name VARCHAR(255),
    product_description VARCHAR(255),
    product_category VARCHAR(255),
    registration_number VARCHAR(255),
    regions_supported VARCHAR(255),
    license_details VARCHAR(255),
    profile_complete BOOLEAN
);

-- Create vendor_documents table if it doesn't exist
CREATE TABLE IF NOT EXISTS vendor_documents (
    id BIGSERIAL PRIMARY KEY,
    vendor_id BIGINT NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    original_file_name VARCHAR(255),
    file_type VARCHAR(255),
    file_size BIGINT,
    description VARCHAR(500),
    upload_date TIMESTAMP NOT NULL,
    FOREIGN KEY (vendor_id) REFERENCES vendor_details(id) ON DELETE CASCADE
);

-- Add comments to tables for documentation
COMMENT ON TABLE customer_details IS 'Stores customer profile information';
COMMENT ON TABLE vendor_details IS 'Stores vendor profile information';
COMMENT ON TABLE vendor_documents IS 'Stores additional documents uploaded by vendors';
