package com.userprofile.user_profile_service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.List;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import com.userprofile.user_profile_service.entity.VendorDetails;
import com.userprofile.user_profile_service.entity.VendorDocument;
import com.userprofile.user_profile_service.repository.VendorDetailsRepository;
import com.userprofile.user_profile_service.repository.VendorDocumentRepository;

@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class VendorDocumentTest {

    @Autowired
    private VendorDetailsRepository vendorDetailsRepository;

    @Autowired
    private VendorDocumentRepository vendorDocumentRepository;

    @Test
    public void testVendorDocumentCreation() {
        // Create a test vendor
        VendorDetails vendor = new VendorDetails();
        vendor.setUserId("test-user-id");
        vendor.setBusinessName("Test Business");
        vendor.setBusinessEmail("<EMAIL>");
        vendor = vendorDetailsRepository.save(vendor);

        // Create a test document
        VendorDocument document = new VendorDocument();
        document.setVendorId(vendor.getId());
        document.setFileName("test_document.pdf");
        document.setOriginalFileName("original_test_document.pdf");
        document.setFileType("application/pdf");
        document.setFileSize(1024L);
        document.setDescription("Test document description");
        
        // Save the document
        document = vendorDocumentRepository.save(document);
        
        // Verify the document was saved
        assertNotNull(document.getId());
        assertNotNull(document.getUploadDate());
        assertEquals("test_document.pdf", document.getFileName());
        assertEquals("original_test_document.pdf", document.getOriginalFileName());
        assertEquals("application/pdf", document.getFileType());
        assertEquals(1024L, document.getFileSize());
        assertEquals("Test document description", document.getDescription());
        assertEquals(vendor.getId(), document.getVendorId());
        
        // Verify we can retrieve the document by vendor ID
        List<VendorDocument> documents = vendorDocumentRepository.findByVendorId(vendor.getId());
        assertEquals(1, documents.size());
        assertEquals(document.getId(), documents.get(0).getId());
    }
}
