package com.userprofile.user_profile_service;

import org.junit.jupiter.api.Test;
import com.userprofile.user_profile_service.entity.VendorDetails;
import static org.assertj.core.api.Assertions.assertThat;

class VendorDetailsTest {

    @Test
    void testVendorDetailsGettersAndSetters() {
        // Create an instance of VendorDetails
        VendorDetails vendor = new VendorDetails();

        // Set values for business basic details
        vendor.setUserId("vendor123");
        vendor.setBusinessName("Tech Solutions Inc.");
        vendor.setBusinessLogo("logo.png");
        vendor.setBusinessEmail("<EMAIL>");
        vendor.setBusinessPhone("**********");
        
        // Set values for business address
        vendor.setStreetAddress("456 Business Ave");
        vendor.setCity("San Francisco");
        vendor.setState("CA");
        vendor.setZipCode("94105");
        vendor.setCountry("USA");
        
        // Set values for business details
        vendor.setBusinessCategory("IT Services");
        vendor.setBusinessSubcategory("Cloud Computing");
        vendor.setBusinessWebsite("www.techsolutions.com");
        vendor.setYearsOfExperience("10");
        vendor.setCertificate("certificate.pdf");
        
        // Set values for product details
        vendor.setProductName("Cloud Manager");
        vendor.setProductDescription("Enterprise cloud management solution");
        vendor.setProductCategory("SaaS");
        
        // Set values for preferences
        vendor.setRegistrationNumber("REG12345");
        vendor.setRegionsSupported("Global");
        vendor.setLicenseDetails("Enterprise License");
        vendor.setProfileComplete(true);

        // Assert values
        assertThat(vendor.getUserId()).isEqualTo("vendor123");
        assertThat(vendor.getBusinessName()).isEqualTo("Tech Solutions Inc.");
        assertThat(vendor.getBusinessEmail()).isEqualTo("<EMAIL>");
        assertThat(vendor.getBusinessPhone()).isEqualTo("**********");
        assertThat(vendor.getStreetAddress()).isEqualTo("456 Business Ave");
        assertThat(vendor.getCity()).isEqualTo("San Francisco");
        assertThat(vendor.getCountry()).isEqualTo("USA");
        assertThat(vendor.getBusinessCategory()).isEqualTo("IT Services");
        assertThat(vendor.getBusinessWebsite()).isEqualTo("www.techsolutions.com");
        assertThat(vendor.getProductName()).isEqualTo("Cloud Manager");
        assertThat(vendor.getRegistrationNumber()).isEqualTo("REG12345");
        assertThat(vendor.isProfileComplete()).isTrue();
    }
}
