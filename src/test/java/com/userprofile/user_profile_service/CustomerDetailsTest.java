package com.userprofile.user_profile_service;

import org.junit.jupiter.api.Test;
import com.userprofile.user_profile_service.entity.CustomerDetails;
import static org.assertj.core.api.Assertions.assertThat;
import java.time.LocalDate;

class CustomerDetailsTest {

    @Test
    void testCustomerDetailsGettersAndSetters() {
        // Create an instance of CustomerDetails
        CustomerDetails customer = new CustomerDetails();

        // Set values for personal information
        customer.setUserId("user123");
        customer.setFirstName("John");
        customer.setLastName("Doe");
        customer.setEmailAddress("<EMAIL>");
        customer.setPhoneNumber("1234567890");
        customer.setDateOfBirth(LocalDate.of(1990, 1, 1));

        // Set values for primary address
        customer.setPrimaryStreetAddress("123 Main St");
        customer.setPrimaryCity("New York");
        customer.setPrimaryState("NY");
        customer.setPrimaryZipCode("10001");
        customer.setCountry("USA");

        // Set values for business details
        customer.setDesignationOrRole("Manager");
        customer.setIndustryOrDomain("Technology");
        customer.setCompanyName("TechCorp");

        // Set values for preferences
        customer.setReceiveNotificationsFromVendors(true);
        customer.setVisibleToVendors(true);
        customer.setProfileComplete(true);

        // Assert values
        assertThat(customer.getUserId()).isEqualTo("user123");
        assertThat(customer.getFirstName()).isEqualTo("John");
        assertThat(customer.getLastName()).isEqualTo("Doe");
        assertThat(customer.getEmailAddress()).isEqualTo("<EMAIL>");
        assertThat(customer.getPhoneNumber()).isEqualTo("1234567890");
        assertThat(customer.getDateOfBirth()).isEqualTo(LocalDate.of(1990, 1, 1));
        assertThat(customer.getPrimaryStreetAddress()).isEqualTo("123 Main St");
        assertThat(customer.getPrimaryCity()).isEqualTo("New York");
        assertThat(customer.getCountry()).isEqualTo("USA");
        assertThat(customer.getDesignationOrRole()).isEqualTo("Manager");
        assertThat(customer.isProfileComplete()).isTrue();
    }
}