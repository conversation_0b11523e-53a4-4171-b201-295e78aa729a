.main-layout {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e2e8f0;
}

.breadcrumb {
  font-size: 14px;
  font-weight: 500;
}

.breadcrumb-purple {
  color: #9e3ca2;
}

.breadcrumb-black {
  color: #1e293b;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.products-selected {
  font-size: 14px;
  color: #64748b;
}

.submit-order-btn {
  background-color: #9e3ca2;
  border: 1px solid #9e3ca2;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.submit-order-btn:hover {
  background-color: #16a34a;
  border-color: #16a34a;
}

/* FIXED: Reduced gap and adjusted column sizes */
.content-layout {
  display: grid;
  grid-template-columns: 300px 1fr; /* Fixed left width, flexible right */
  gap: 20px; /* Reduced from 32px */
}

.left-column {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 16px 0;
}

.product-box {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  width: 100%; /* Changed from fixed 280px */
  height: 280px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.starting-price {
  font-size: 12px;
  color: #64748b;
}

.status-badge {
  background-color: #9e3ca2;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.product-name {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 12px 0;
}

.product-details {
  margin-bottom: 12px;
  flex-grow: 1;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid #f1f5f9;
}

.price-row.highlight {
  color: #9e3ca2;
  font-weight: 600;
}

.date-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  margin-top: 8px;
}

.date-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.label {
  font-size: 12px;
  color: #64748b;
}

.value {
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
}

.product-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #f1f5f9;
}

.vendor-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.vendor-name {
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
}

.rating {
  display: flex;
  gap: 2px;
}

.star {
  color: #d1d5db;
  font-size: 14px;
}

.star.filled {
  color: #fbbf24;
}

.selected-btn {
  background-color: #9e3ca2;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
}

.right-column {
  display: flex;
  width: 100%; /* Ensure full width usage */
}

/* FIXED: Make container take full available width */
.create-order-container {
  width: 100%; /* Changed from max-width: 600px */
  min-height: 500px;
  padding: 24px;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  background-color: white;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 24px;
}

.submit-btn {
  background-color: #9e3ca2;
  border: 1px solid #9e3ca2;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.submit-btn:hover {
  background-color: #16a34a;
  border-color: #16a34a;
}

.selected-products {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.product-card {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
  background-color: #f8fafc;
}

.product-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.product-select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
}

.service-name {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.service-name h3 {
  font-size: 18px;
  color: #1e293b;
  margin: 0;
  text-transform: capitalize;
}

.price-details {
  display: flex;
  gap: 24px;
  padding: 16px 0;
  border-top: 1px solid #e2e8f0;
  border-bottom: 1px solid #e2e8f0;
  flex-wrap: wrap;
}

.price-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 150px;
  flex: 1;
}

.price-label {
  font-size: 14px;
  color: #64748b;
}

.price-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.currency {
  font-size: 16px;
  font-weight: 500;
}

.currency.highlight {
  color: #9e3ca2;
}

.price-input {
  width: 100px;
  padding: 8px 10px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.price-input.highlight {
  color: #9e3ca2;
}

.date-section {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.date-picker {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  padding: 20px 24px 0;
  border-bottom: 1px solid #e2e8f0;
}

.modal-header h3 {
  font-size: 18px;
  color: #1e293b;
  margin: 0;
  padding-bottom: 16px;
}

.modal-body {
  padding: 24px;
}

.modal-body p {
  font-size: 16px;
  color: #64748b;
  margin: 0 0 20px 0;
  line-height: 1.5;
}

.order-summary {
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 16px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.summary-label {
  font-size: 14px;
  color: #64748b;
}

.summary-value {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
}

.summary-value.highlight {
  color: #9e3ca2;
}

.modal-footer {
  padding: 0 24px 24px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.btn-cancel {
  background-color: white;
  border: 1px solid #d1d5db;
  color: #64748b;
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-cancel:hover {
  background-color: #f8fafc;
  border-color: #9ca3af;
}

.btn-confirm {
  background-color: #9e3ca2;
  border: 1px solid #9e3ca2;
  color: white;
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-confirm:hover {
  background-color: #16a34a;
  border-color: #16a34a;
}

/* Responsive styles */
@media (max-width: 1024px) {
  .content-layout {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .create-order-container {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .main-layout {
    padding: 16px;
  }

  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .product-box {
    width: 100%;
    height: auto;
    min-height: 250px;
  }

  .price-details {
    flex-direction: column;
    gap: 16px;
  }

  .date-section {
    flex-direction: column;
  }

  .modal-content {
    margin: 20px;
    width: calc(100% - 40px);
  }
}