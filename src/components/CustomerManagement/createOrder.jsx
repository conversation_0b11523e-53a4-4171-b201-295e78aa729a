import React, { useState } from 'react';
import './CreateOrder.css';
import Header from '../Header-Section/Header';
import { useNavigate } from 'react-router-dom';





// Utility functions for in-memory storage (instead of localStorage)
let ordersStorage = [];
const generateOrderId = () => {
  return `ORDER_${Date.now()}`;
};

const saveOrderToStorage = (orderData) => {
  try {
    const newOrder = {
      id: generateOrderId(),
      productName: orderData.selectedProduct,
      startDate: orderData.startDate,
      endDate: orderData.endDate,
      orderStatus: "awarded",
      status: "approved",
      vendor: "ABC Consulting",
      category: mapProductToCategory(orderData.selectedProduct)
    };
    
    ordersStorage.push(newOrder);
    return newOrder;
  } catch (error) {
    console.error('Error saving order:', error);
    return null;
  }
};

const mapProductToCategory = (product) => {
  const categoryMap = {
    'Network Monitoring': 'Network Services',
    'Web Hosting Services': 'Hosting',
    'Enterprise Cloud Hosting': 'Cloud Services',
    'Managed IT Services': 'IT Services',
    'Edge cloud storage': 'Cloud Storage'
  };
  return categoryMap[product] || 'General Services';
};

const CreateOrder = () => {
  const [selectedProduct, setSelectedProduct] = useState('Edge cloud storage');
  const [startingPrice, setStartingPrice] = useState('99');
  const [offerPrice, setOfferPrice] = useState('79');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [showModal, setShowModal] = useState(false);


    const navigate = useNavigate();

  const productOptions = [
    'Network Monitoring',
    'Web Hosting Services',
    'Enterprise Cloud Hosting',
    'Managed IT Services',
    'Edge cloud storage'
  ];

  const handleSubmit = () => {
    setShowModal(true);
  };

  const handleConfirm = () => {
    if (!selectedProduct || !startDate || !endDate) {
      alert('Please fill in all required fields');
      return;
    }

    const orderData = {
      selectedProduct,
      startingPrice: parseFloat(startingPrice) || 0,
      offerPrice: parseFloat(offerPrice) || 0,
      startDate,
      endDate
    };

    const savedOrder = saveOrderToStorage(orderData);
    
    if (savedOrder) {
      console.log('Order saved successfully:', savedOrder);
      setShowModal(false);
      navigate('/order');
    } else {
      alert('Error creating order. Please try again.');
    }
  };

  const handleCancel = () => {
    setShowModal(false);
  };

  return (
    <>
    <Header />
    <div className="main-layout">
      {/* Header */}
    
      <div className="header">
        <div className="breadcrumb">
          <span className="breadcrumb-purple">Products/</span>
          <span className="breadcrumb-black">Create Order</span>
        </div>
        <div className="header-info">
          <span className="products-selected">04 Products Selected</span>
        </div>
      </div>

      {/* Two Column Layout */}
      <div className="content-layout">
        {/* Left Column - Hardcoded Products */}
        <div className="left-column">
          <h3 className="section-title">Selected Products</h3>
          
          {/* Product 1 - Edge Cloud Storage */}
          <div className="product-box">
            <div className="product-header">
              <span className="starting-price">Starting at $99/M</span>
            </div>
            <h4 className="product-name">Edge Cloud Storage</h4>
            <div className="product-details">
              <div className="price-row">
                <span className="label">Starting Price</span>
                <span className="value">$99/M</span>
              </div>
              <div className="price-row highlight">
                <span className="label">Offer Price</span>
                <span className="value">$ 79</span>
              </div>
              <div className="date-row">
                <div className="date-group">
                  <span className="label">Start Date</span>
                  <span className="value">29/Apr/2025</span>
                </div>
                <div className="date-group">
                  <span className="label">End Date</span>
                  <span className="value">29/Apr/2025</span>
                </div>
              </div>
            </div>
            <div className="product-footer">
              <div className="vendor-info">
                <span className="vendor-name">ABC Clouds</span>
                <div className="rating">
                  {[1, 2, 3, 4, 5].map(star => (
                    <span key={star} className={`star ${star <= 4 ? 'filled' : ''}`}>★</span>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Product 2 - Enterprise Cloud Hosting */}
          <div className="product-box">
            <div className="product-header">
              <span className="starting-price">Starting at $99/M</span>
            </div>
            <h4 className="product-name">Enterprise Cloud Hosting</h4>
            <div className="product-details">
              <div className="price-row">
                <span className="label">Starting Price</span>
                <span className="value">$99/M</span>
              </div>
              <div className="price-row highlight">
                <span className="label">Offer Price</span>
                <span className="value">$ 49</span>
              </div>
              <div className="date-row">
                <div className="date-group">
                  <span className="label">Start Date</span>
                  <span className="value">29/Apr/2025</span>
                </div>
                <div className="date-group">
                  <span className="label">End Date</span>
                  <span className="value">29/Apr/2025</span>
                </div>
              </div>
            </div>
            <div className="product-footer">
              <div className="vendor-info">
                <span className="vendor-name">ABC Clouds</span>
                <div className="rating">
                  {[1, 2, 3, 4, 5].map(star => (
                    <span key={star} className={`star ${star <= 4 ? 'filled' : ''}`}>★</span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column - Create Order Container */}
        <div className="right-column">
          <div className="create-order-container">
            <div className="order-header">
              <div className="breadcrumb">
              </div>
              <button 
                className="submit-btn"
                onClick={handleSubmit}
              >
                Create
              </button>
            </div>

            <div className="selected-products">
              <div className="product-card">
                <div className="product-info">
                  {/* Product Selection */}
                  <div className="form-group">
                    <label className="form-label">
                      Select Product
                    </label>
                    <select
                      value={selectedProduct}
                      onChange={(e) => setSelectedProduct(e.target.value)}
                      className="product-select"
                    >
                      {productOptions.map(option => (
                        <option key={option} value={option}>
                          {option}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Service Name and Rating */}
                  <div className="service-name">
                    <h3>{selectedProduct}</h3>
                    <div className="rating">
                      {[1, 2, 3, 4, 5].map(star => (
                        <span 
                          key={star}
                          className={`star ${star <= 4 ? 'filled' : ''}`}
                        >
                          ★
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Price Details */}
                  <div className="price-details">
                    <div className="price-section">
                      <div className="price-label">
                        Starting Price
                      </div>
                      <div className="price-input-group">
                        <span className="currency">SAR</span>
                        <input
                          type="text"
                          value={startingPrice}
                          onChange={(e) => {
                            const value = e.target.value;
                            if (value === '' || /^\d*\.?\d*$/.test(value)) {
                              setStartingPrice(value);
                            }
                          }}
                          placeholder="0"
                          className="price-input"
                        />
                      </div>
                    </div>

                    <div className="price-section">
                      <div className="price-label">
                        Offer Price
                      </div>
                      <div className="price-input-group">
                        <span className="currency highlight">SAR</span>
                        <input
                          type="text"
                          value={offerPrice}
                          onChange={(e) => {
                            const value = e.target.value;
                            if (value === '' || /^\d*\.?\d*$/.test(value)) {
                              setOfferPrice(value);
                            }
                          }}
                          placeholder="0"
                          className="price-input highlight"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Date Section */}
                  <div className="date-section">
                    <div className="date-group">
                      <label className="form-label">
                        Start Date
                      </label>
                      <input
                        type="date"
                        value={startDate}
                        onChange={(e) => setStartDate(e.target.value)}
                        className="date-picker"
                      />
                    </div>
                    <div className="date-group">
                      <label className="form-label">
                        End Date
                      </label>
                      <input
                        type="date"
                        value={endDate}
                        onChange={(e) => setEndDate(e.target.value)}
                        className="date-picker"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Confirmation Modal */}
      {showModal && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3>Confirm Order Creation</h3>
            </div>
            <div className="modal-body">
              <p>Are you sure you want to confirm and proceed with creating this order?</p>
              <div className="order-summary">
                <div className="summary-item">
                  <span className="summary-label">Service:</span>
                  <span className="summary-value">{selectedProduct}</span>
                </div>
                <div className="summary-item">
                  <span className="summary-label">Starting Price:</span>
                  <span className="summary-value">SAR {startingPrice || '0'}</span>
                </div>
                <div className="summary-item">
                  <span className="summary-label">OfferPrice:</span>
                  <span className="summary-value highlight">SAR {offerPrice || '0'}</span>
                </div>
                {startDate && (
                  <div className="summary-item">
                    <span className="summary-label">Start Date:</span>
                    <span className="summary-value">{startDate}</span>
                  </div>
                )}
                {endDate && (
                  <div className="summary-item">
                    <span className="summary-label">End Date:</span>
                    <span className="summary-value">{endDate}</span>
                  </div>
                )}
              </div>
            </div>
            <div className="modal-footer">
              <button 
                className="btn-cancel"
                onClick={handleCancel}
              >
                Cancel
              </button>
              <button 
                className="btn-confirm"
                onClick={handleConfirm}
              >
                Confirm & Proceed
              </button>
            </div>
          </div>
        </div>
      )}
 </div>
     </>

  );
};
export default CreateOrder;